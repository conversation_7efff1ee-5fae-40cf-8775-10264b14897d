<h1 align="center"> ⛅️ wrangler </h1>
<section align="center" id="shieldio-badges">
<a href="https://www.npmjs.com/package/wrangler"><img alt="npm"  src="https://img.shields.io/npm/dw/wrangler?style=flat-square"></a>
<img alt="GitHub contributors" src="https://img.shields.io/github/contributors/cloudflare/workers-sdk?style=flat-square">
<img alt="GitHub commit activity (branch)" src="https://img.shields.io/github/commit-activity/w/cloudflare/workers-sdk/main?style=flat-square">
<a href="https://discord.cloudflare.com"><img alt="Discord" src="https://img.shields.io/discord/595317990191398933?color=%23F48120&style=flat-square"></a>
</section>

`wrangler` is a command line tool for building [Cloudflare Workers](https://workers.cloudflare.com/).

## Quick Start

To get started quickly with a Hello World worker, run the command below:

```bash
npx wrangler init my-worker -y
```

For more info, visit our [Getting Started](https://developers.cloudflare.com/workers/get-started/guide/) guide.

<details><summary>Wrangler System Requirements</summary>

We support running the Wrangler CLI with the [Current, Active, and Maintenance](https://nodejs.org/en/about/previous-releases) versions of Node.js. Your Worker will always be executed in `workerd`, the open source Cloudflare Workers runtime.

Wrangler is only supported on macOS 13.5+, Windows 11, and Linux distros that support glib 2.35. This follows [`workerd`'s OS support policy](https://github.com/cloudflare/workerd?tab=readme-ov-file#running-workerd).

</details>

## Documentation

For the latest Wrangler documentation, [click here](https://developers.cloudflare.com/workers/wrangler/).

To read more about Workers in general:

- [Getting Started](https://developers.cloudflare.com/workers/get-started/guide/)
- [How Workers works](https://developers.cloudflare.com/workers/reference/how-workers-works/)
- [Observability](https://developers.cloudflare.com/workers/observability/)
- [Platform](https://developers.cloudflare.com/workers/platform/)

## Configuration

Wrangler is configured via a `wrangler.toml` or `wrangler.json` file in the project root. An example configuration generated by `npx wrangler init` or `npx create cloudflare` is as follows:

```toml
name = "my-worker"
main = "./src/index.ts"
compatibility_date = "YYYY-MM-DD"
```

For more detailed information about configuration, refer to the [documentation](https://developers.cloudflare.com/workers/wrangler/configuration/).

## Commands

### Workers

#### `wrangler dev`

Start a local development server, with live reloading and devtools.

#### `wrangler deploy`

Publish the given script to Cloudflare's global network.

For more commands and options, refer to the [documentation](https://developers.cloudflare.com/workers/wrangler/commands/).

### Pages

#### `wrangler pages dev [directory]`

Serves a static build asset directory.

Builds and runs functions from a `./functions` directory or uses a `_worker.js` file inside the static build asset directory.

For more commands and options, refer to the [documentation](https://developers.cloudflare.com/pages/platform/functions#develop-and-preview-locally) or run `wrangler pages dev --help`.
