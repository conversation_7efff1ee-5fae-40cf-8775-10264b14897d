import { sign, verify } from 'hono/jwt';
import { JWTPayload } from '../types/types';

export class JWTService {
  private secret: string;

  constructor(secret: string) {
    this.secret = secret;
  }

  async generateToken(payload: Omit<JWTPayload, 'exp' | 'iat'>): Promise<string> {
    const now = Math.floor(Date.now() / 1000);
    const jwtPayload: JWTPayload = {
      ...payload,
      iat: now,
      exp: now + 24 * 60 * 60, // 24小时过期
    };
    return await sign(jwtPayload, this.secret);
  }

  async generateRefreshToken(payload: Omit<JWTPayload, 'exp' | 'iat'>): Promise<string> {
    const now = Math.floor(Date.now() / 1000);
    const jwtPayload: JWTPayload = {
      ...payload,
      iat: now,
      exp: now + 7 * 24 * 60 * 60, // 7天过期
    };
    return await sign(jwtPayload, this.secret);
  }

  async verifyToken(token: string): Promise<JWTPayload> {
    return await verify(token, this.secret) as JWTPayload;
  }
}

export function generateRandomKey(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}